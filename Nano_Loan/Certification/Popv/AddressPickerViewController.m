//
//  AddressPickerViewController.m
//  Nano_Loan
//
//  Created by iOSDev on 2025/06/26.
//

#import "AddressPickerViewController.h"
#import "AddressNode.h"

@interface AddressPickerViewController () <UIPickerViewDataSource, UIPickerViewDelegate>

@property (nonatomic, copy) NSArray<AddressNode *> *roots;
@property (nonatomic, copy) AddressPickerResult resultBlock;

@property (nonatomic, strong) UIView *container;
@property (nonatomic, strong) UIPickerView *picker;

@property (nonatomic, assign) NSInteger selectedProvince;
@property (nonatomic, assign) NSInteger selectedCity;
@property (nonatomic, assign) NSInteger selectedDistrict;

@end

@implementation AddressPickerViewController

- (instancetype)initWithAddressRoots:(NSArray<AddressNode *> *)roots
                           selection:(AddressPickerResult)result {
    self = [super init];
    if (self) {
        _roots = roots;
        _resultBlock = [result copy];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.4];

    // 点击背景关闭
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissSelf)];
    [self.view addGestureRecognizer:tap];

    // container
    CGFloat height = 260 + 44; // toolbar + picker
    self.container = [[UIView alloc] initWithFrame:CGRectMake(0, self.view.bounds.size.height - height, self.view.bounds.size.width, height)];
    self.container.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    self.container.backgroundColor = UIColor.systemBackgroundColor;
    [self.view addSubview:self.container];

    // toolbar
    UIToolbar *toolbar = [[UIToolbar alloc] initWithFrame:CGRectMake(0, 0, self.container.bounds.size.width, 44)];
    toolbar.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    UIBarButtonItem *cancel = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemCancel target:self action:@selector(dismissSelf)];
    UIBarButtonItem *flex = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace target:nil action:nil];
    UIBarButtonItem *done = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemDone target:self action:@selector(doneTapped)];
    toolbar.items = @[cancel, flex, done];
    [self.container addSubview:toolbar];

    // picker
    self.picker = [[UIPickerView alloc] initWithFrame:CGRectMake(0, 44, self.container.bounds.size.width, 260)];
    self.picker.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleTopMargin;
    self.picker.dataSource = self;
    self.picker.delegate = self;
    [self.container addSubview:self.picker];
}

#pragma mark - Actions

- (void)dismissSelf {
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)doneTapped {
    if (self.resultBlock) {
        AddressNode *province = self.roots.count > self.selectedProvince ? self.roots[self.selectedProvince] : nil;
        AddressNode *city = province.children.count > self.selectedCity ? province.children[self.selectedCity] : nil;
        AddressNode *district = city.children.count > self.selectedDistrict ? city.children[self.selectedDistrict] : nil;
        self.resultBlock(province, city, district);
    }
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - UIPickerViewDataSource

- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView {
    return 3;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component {
    if (component == 0) {
        return self.roots.count;
    } else if (component == 1) {
        AddressNode *province = self.roots.count > self.selectedProvince ? self.roots[self.selectedProvince] : nil;
        return province.children.count;
    } else {
        AddressNode *province = self.roots.count > self.selectedProvince ? self.roots[self.selectedProvince] : nil;
        AddressNode *city = province.children.count > self.selectedCity ? province.children[self.selectedCity] : nil;
        return city.children.count;
    }
}

#pragma mark - UIPickerViewDelegate

- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component {
    if (component == 0) {
        return self.roots[row].name;
    } else if (component == 1) {
        AddressNode *province = self.roots[self.selectedProvince];
        return province.children[row].name;
    } else {
        AddressNode *province = self.roots[self.selectedProvince];
        AddressNode *city = province.children[self.selectedCity];
        return city.children[row].name;
    }
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component {
    if (component == 0) {
        self.selectedProvince = row;
        self.selectedCity = 0;
        self.selectedDistrict = 0;
        [pickerView reloadComponent:1];
        [pickerView reloadComponent:2];
        [pickerView selectRow:0 inComponent:1 animated:YES];
        [pickerView selectRow:0 inComponent:2 animated:YES];

        // 自动跳转到二级地址选择
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 模拟用户点击二级地址组件，使其获得焦点
            [self focusOnComponent:1 inPickerView:pickerView];
        });
    } else if (component == 1) {
        self.selectedCity = row;
        self.selectedDistrict = 0;
        [pickerView reloadComponent:2];
        [pickerView selectRow:0 inComponent:2 animated:YES];

        // 自动跳转到三级地址选择
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self focusOnComponent:2 inPickerView:pickerView];
        });
    } else {
        self.selectedDistrict = row;
    }
}

#pragma mark - Private Methods

- (void)focusOnComponent:(NSInteger)component inPickerView:(UIPickerView *)pickerView {
    // 通过轻微的视觉反馈来引导用户注意到组件切换
    // 可以通过短暂高亮当前选中的行来实现焦点效果

    // 获取当前选中的行
    NSInteger selectedRow = [pickerView selectedRowInComponent:component];

    // 重新选择当前行，带动画效果，给用户视觉提示
    [pickerView selectRow:selectedRow inComponent:component animated:YES];

    // 可选：添加轻微的震动反馈
    if (@available(iOS 10.0, *)) {
        UIImpactFeedbackGenerator *feedbackGenerator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleLight];
        [feedbackGenerator impactOccurred];
    }
}

@end